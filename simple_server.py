#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import http.server
import socketserver
import webbrowser
import os
import sys
from urllib.parse import urlparse, parse_qs
import json
import urllib.request
import urllib.error

PORT = 3000

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, appkey, appsecret, tr_id')
        super().end_headers()

    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

    def do_POST(self):
        if self.path.startswith('/api/'):
            self.handle_api_request()
        else:
            super().do_POST()

    def do_GET(self):
        if self.path.startswith('/api/'):
            self.handle_api_request()
        else:
            super().do_GET()

    def handle_api_request(self):
        try:
            # API 프록시 처리
            if self.path == '/api/token':
                self.handle_token_request()
            elif self.path.startswith('/api/us-stock/'):
                self.handle_stock_request()
            else:
                self.send_error(404, "API endpoint not found")
        except Exception as e:
            self.send_error(500, str(e))

    def handle_token_request(self):
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            # 한국투자증권 API 호출
            kis_url = f"https://openapivts.koreainvestment.com:29443/oauth2/tokenP" if data['environment'] == 'vps' else f"https://openapi.koreainvestment.com:9443/oauth2/tokenP"
            
            req_data = {
                'grant_type': 'client_credentials',
                'appkey': data['appkey'],
                'appsecret': data['appsecret']
            }
            
            req = urllib.request.Request(
                kis_url,
                data=json.dumps(req_data).encode('utf-8'),
                headers={'Content-Type': 'application/json'}
            )
            
            with urllib.request.urlopen(req) as response:
                result = json.loads(response.read().decode('utf-8'))
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(result).encode('utf-8'))
            
        except Exception as e:
            self.send_error(500, f"Token request failed: {str(e)}")

    def handle_stock_request(self):
        # 주식 API 요청 처리 (간단한 예시)
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        
        # 모의 데이터 반환 (실제로는 KIS API 호출)
        mock_data = {
            "rt_cd": "0",
            "msg_cd": "MCA00000",
            "msg1": "정상처리 되었습니다.",
            "output": {
                "last": "150.00",
                "rate": "2.5",
                "tvol": "1000000"
            }
        }
        self.wfile.write(json.dumps(mock_data).encode('utf-8'))

def start_server():
    print("=" * 50)
    print("🚀 한국투자증권 미국주식 자동매매 시스템")
    print("   Korea Investment US Stock Auto Trading")
    print("=" * 50)
    print()
    
    # 현재 디렉토리 확인
    current_dir = os.getcwd()
    print(f"📁 현재 디렉토리: {current_dir}")
    
    # index.html 파일 존재 확인
    if not os.path.exists('index.html'):
        print("❌ 오류: index.html 파일을 찾을 수 없습니다.")
        print("   프로젝트 폴더에서 실행해주세요.")
        input("엔터를 눌러 종료...")
        return
    
    try:
        with socketserver.TCPServer(("", PORT), CORSHTTPRequestHandler) as httpd:
            print(f"🌐 서버가 포트 {PORT}에서 실행 중입니다.")
            print(f"📱 브라우저에서 http://localhost:{PORT} 으로 접속하세요.")
            print()
            print("⚠️  주의사항:")
            print("   - 실제 거래 전에 모의투자로 충분히 테스트하세요!")
            print("   - CORS 문제가 발생할 수 있습니다.")
            print("   - 종료하려면 Ctrl+C를 누르세요.")
            print()
            
            # 자동으로 브라우저 열기
            try:
                webbrowser.open(f'http://localhost:{PORT}')
                print("🔗 브라우저가 자동으로 열렸습니다.")
            except:
                print("🔗 브라우저를 수동으로 열어주세요.")
            
            print()
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 10048:  # Windows: Address already in use
            print(f"❌ 오류: 포트 {PORT}이 이미 사용 중입니다.")
            print("   다른 프로그램을 종료하거나 다른 포트를 사용하세요.")
        else:
            print(f"❌ 서버 시작 오류: {e}")
        input("엔터를 눌러 종료...")
    except KeyboardInterrupt:
        print("\n👋 서버를 종료합니다.")

if __name__ == "__main__":
    start_server()
