# 한국투자증권 미국주식 자동매매 시스템 (업데이트됨)

한국투자증권 OPEN API를 사용한 미국주식 자동매매 프로그램입니다. Node.js 서버 기반으로 완전한 기능을 제공하며, HTML 웹 인터페이스를 통해 쉽게 사용할 수 있습니다.

## 🆕 최신 업데이트 (v2.0)

- ✅ **Node.js v22.18.0 완전 지원**
- ✅ **CORS 문제 완전 해결**
- ✅ **실시간 서버 상태 모니터링**
- ✅ **자동 브라우저 실행**
- ✅ **향상된 오류 처리**
- ✅ **SSL 인증서 문제 해결**

## 🚀 주요 기능

- **API 키 로그인**: 한국투자증권 API 키를 통한 안전한 인증
- **자동매매 전략**: 개장 후 3분 후부터 시작되는 지능형 매매 전략
- **실시간 모니터링**: 주요 미국 주식의 실시간 가격 및 거래량 모니터링
- **포지션 관리**: 자동 손절/익절 시스템
- **웹 인터페이스**: 직관적인 HTML 대시보드

## 📈 매매 전략

1. **진입 조건**:
   - 개장 후 3분 후부터 모니터링 시작
   - 1분봉 전고점 돌파
   - 1분 거래액 $80 이상
   - 일일 상승률 20% 이상
   - 1분봉 상승률 5% 이상

2. **포지션 관리**:
   - 매수: $1000 고정 금액
   - 손절: -5%
   - 부분 익절: +10% (50% 매도)
   - 최종 익절: 최고가 대비 -18%

## 🛠️ 설치 방법

### 1. 필수 요구사항
- Node.js 14.0.0 이상
- 한국투자증권 계좌 및 OPEN API 신청

### 2. 프로젝트 설치
```bash
# 저장소 클론 또는 파일 다운로드
git clone <repository-url>
cd hantoo

# 의존성 설치
npm install
```

### 3. 서버 실행
```bash
# 개발 모드 (자동 재시작)
npm run dev

# 또는 일반 실행
npm start
```

### 4. 웹 브라우저 접속
```
http://localhost:3000
```

## 🔑 API 설정

### 1. 한국투자증권 OPEN API 신청
1. [한국투자증권 개발자센터](https://apiportal.koreainvestment.com/) 접속
2. 계좌 연결 및 API 서비스 신청
3. App Key와 App Secret 발급

### 2. 웹 인터페이스에서 설정
1. 거래 환경 선택 (모의투자/실전투자)
2. App Key 입력
3. App Secret 입력
4. 계좌번호 앞 8자리 입력
5. 계좌상품코드 선택

## 📱 사용 방법

### 1. 로그인
- 웹 페이지에서 API 정보 입력 후 로그인

### 2. 자동매매 시작
- "자동매매 시작" 버튼 클릭
- 시스템이 3분 후 자동으로 모니터링 시작

### 3. 모니터링
- 실시간 로그에서 거래 상황 확인
- 포지션 현황에서 보유 종목 확인

### 4. 중지
- "자동매매 중지" 버튼으로 언제든 중지 가능

## ⚠️ 주의사항

### 🔴 중요한 경고
- **실제 거래 전에 반드시 모의투자로 충분히 테스트하세요**
- **투자 손실에 대한 책임은 사용자에게 있습니다**
- **API 호출 제한을 준수하세요**

### 📋 사용 전 체크리스트
- [ ] 모의투자 환경에서 테스트 완료
- [ ] 매매 전략 이해 및 동의
- [ ] 손실 허용 범위 설정
- [ ] API 키 보안 관리

## 🔧 기술 스택

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Backend**: Node.js, Express.js
- **API**: 한국투자증권 OPEN API
- **기타**: CORS 프록시, 실시간 데이터 처리

## 📊 모니터링 종목

기본적으로 다음 종목들을 모니터링합니다:
- AAPL (Apple)
- TSLA (Tesla)
- NVDA (NVIDIA)
- MSFT (Microsoft)
- GOOGL (Alphabet)
- AMZN (Amazon)
- META (Meta)

## 🐛 문제 해결

### 토큰 오류
- 브라우저 새로고침 후 재로그인
- API 키 정보 재확인

### 주문 실패
- 계좌 잔고 확인
- 거래 시간 확인 (미국 시장 개장 시간)
- 모의투자 환경 확인

### 연결 오류
- 서버 실행 상태 확인
- 포트 3000 사용 가능 여부 확인

## 📞 지원

- 한국투자증권 고객센터: 1544-5000
- 개발자센터: https://apiportal.koreainvestment.com/

## 📄 라이선스

이 프로젝트는 교육 및 개인 사용 목적으로 제공됩니다.

---

**⚠️ 면책 조항**: 이 소프트웨어는 교육 목적으로 제공되며, 실제 투자 손실에 대해 개발자는 책임지지 않습니다. 모든 투자 결정은 사용자의 책임입니다.
