@echo off
echo ========================================
echo 한국투자증권 미국주식 자동매매 시스템
echo Korea Investment US Stock Auto Trading
echo ========================================
echo.

echo 실행 환경 확인 중...

REM Node.js 확인
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Node.js가 설치되어 있습니다.
    echo Node.js 서버로 실행합니다...
    echo.

    echo 의존성 설치 중...
    call npm install
    if %errorlevel% neq 0 (
        echo 의존성 설치에 실패했습니다. Python 서버로 전환합니다...
        goto :python_server
    )

    echo 서버 시작 중...
    call npm start
    goto :end
)

:python_server
echo Node.js가 설치되지 않았습니다.
echo Python 서버로 실행합니다...
echo.

REM Python 확인
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 오류: Python도 설치되지 않았습니다.
    echo.
    echo 다음 중 하나를 설치해주세요:
    echo 1. Node.js: https://nodejs.org
    echo 2. Python: https://python.org
    echo.
    pause
    exit /b 1
)

echo Python 서버 시작 중...
python simple_server.py

:end
pause
