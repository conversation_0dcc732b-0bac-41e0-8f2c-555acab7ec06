#!/bin/bash

echo "========================================"
echo "한국투자증권 미국주식 자동매매 시스템"
echo "Korea Investment US Stock Auto Trading"
echo "========================================"
echo

echo "Node.js 버전 확인 중..."
if ! command -v node &> /dev/null; then
    echo "오류: Node.js가 설치되지 않았습니다."
    echo "Node.js를 https://nodejs.org 에서 다운로드하여 설치하세요."
    exit 1
fi

node --version
echo

echo "의존성 설치 중..."
npm install
if [ $? -ne 0 ]; then
    echo "오류: 의존성 설치에 실패했습니다."
    exit 1
fi

echo
echo "서버 시작 중..."
echo "브라우저에서 http://localhost:3000 으로 접속하세요."
echo "종료하려면 Ctrl+C를 누르세요."
echo

npm start
