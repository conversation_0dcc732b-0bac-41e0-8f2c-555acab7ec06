@echo off
echo ========================================
echo 한국투자증권 미국주식 자동매매 시스템
echo 빠른 실행 (Quick Start)
echo ========================================
echo.

echo 1. 독립 실행 버전 열기...
start "" "standalone.html"

echo.
echo 2. Python 서버 시도 중...

REM Python 3.x 시도
py -m http.server 3000 2>nul
if %errorlevel% equ 0 (
    echo Python 서버가 시작되었습니다!
    echo 브라우저에서 http://localhost:3000 으로 접속하세요.
    start "" "http://localhost:3000"
    goto :end
)

REM Python 2.x 시도
python -m SimpleHTTPServer 3000 2>nul
if %errorlevel% equ 0 (
    echo Python 서버가 시작되었습니다!
    echo 브라우저에서 http://localhost:3000 으로 접속하세요.
    start "" "http://localhost:3000"
    goto :end
)

echo.
echo Python 서버 시작에 실패했습니다.
echo 독립 실행 버전(standalone.html)을 사용하세요.
echo.

:end
echo.
echo 프로그램이 실행되었습니다!
echo 창을 닫으려면 아무 키나 누르세요.
pause >nul
