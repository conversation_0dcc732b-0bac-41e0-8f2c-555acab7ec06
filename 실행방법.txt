========================================
한국투자증권 미국주식 자동매매 시스템
실행 방법 안내
========================================

🚀 빠른 시작 (추천)
==================

1. Node.js 설치 (가장 권장)
   - https://nodejs.org 접속
   - LTS 버전 다운로드 및 설치
   - 설치 후 start.bat 더블클릭

2. Python 설치 (대안)
   - https://python.org 접속  
   - 최신 버전 다운로드 및 설치
   - 설치 후 start.bat 더블클릭

3. 임시 사용 (제한적)
   - standalone.html 파일을 더블클릭
   - 브라우저에서 바로 실행 (데모 모드)


📋 상세 실행 단계
================

방법 1: Node.js 서버 (완전 기능)
-------------------------------
1. Node.js 설치 확인
   - 명령 프롬프트에서: node --version
   
2. 프로젝트 폴더에서 실행
   - start.bat 더블클릭 또는
   - 명령 프롬프트에서: npm install → npm start
   
3. 브라우저에서 접속
   - http://localhost:3000

방법 2: Python 서버 (완전 기능)  
------------------------------
1. Python 설치 확인
   - 명령 프롬프트에서: python --version
   
2. 프로젝트 폴더에서 실행
   - start.bat 더블클릭 또는
   - 명령 프롬프트에서: python simple_server.py
   
3. 브라우저에서 접속
   - http://localhost:3000

방법 3: 독립 실행 (제한적)
-------------------------
1. standalone.html 더블클릭
2. 브라우저에서 바로 실행
3. 데모 모드로만 동작 (실제 API 호출 제한)


⚠️ 중요 주의사항
===============

1. 실제 거래 전 필수 확인사항
   - 모의투자로 충분히 테스트
   - 매매 전략 완전 이해
   - 손실 허용 범위 설정
   - API 키 보안 관리

2. API 설정 필요사항
   - 한국투자증권 계좌 개설
   - OPEN API 서비스 신청
   - App Key, App Secret 발급
   - 계좌번호 확인

3. 기술적 제한사항
   - CORS 정책으로 인한 브라우저 제한
   - API 호출 횟수 제한 준수
   - 네트워크 연결 상태 확인


🔧 문제 해결
===========

연결 오류 (ERR_CONNECTION_REFUSED)
---------------------------------
- Node.js 또는 Python 미설치
- 포트 3000 사용 중
- 방화벽 차단
→ 해결: 프로그램 설치 후 start.bat 실행

API 호출 오류
------------
- CORS 정책 차단
- 잘못된 API 키
- 네트워크 연결 문제
→ 해결: 서버 버전 사용, API 키 재확인

토큰 발급 실패
-------------
- 잘못된 App Key/Secret
- API 서비스 미신청
- 계좌 연결 문제
→ 해결: 한국투자증권에서 API 설정 재확인


📞 지원 및 문의
==============

- 한국투자증권 고객센터: 1544-5000
- 개발자센터: https://apiportal.koreainvestment.com
- API 문서: 개발자센터에서 확인


📄 파일 설명
===========

- index.html: 메인 웹 인터페이스 (서버 필요)
- standalone.html: 독립 실행 버전 (제한적)
- server.js: Node.js 서버
- simple_server.py: Python 서버
- start.bat: 자동 실행 스크립트
- package.json: Node.js 의존성 정의


🎯 성공적인 실행을 위한 체크리스트
===============================

□ Node.js 또는 Python 설치 완료
□ 한국투자증권 API 키 발급 완료
□ 계좌번호 확인 완료
□ 모의투자 환경에서 테스트 완료
□ 매매 전략 이해 완료
□ 손실 허용 범위 설정 완료

모든 항목을 확인한 후 실행하세요!

========================================
⚠️ 면책 조항: 투자 손실에 대한 책임은 사용자에게 있습니다.
========================================
