<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>한국투자증권 미국주식 자동매매 시스템 (독립실행)</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 1000px;
            min-height: 600px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .main-content {
            padding: 30px;
        }

        .warning-panel {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .warning-title {
            font-weight: 600;
            color: #856404;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .warning-item {
            margin-bottom: 10px;
            color: #856404;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: transform 0.2s;
            width: 100%;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        }

        .alert {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .info-panel {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .info-title {
            font-weight: 600;
            color: #0056b3;
            margin-bottom: 15px;
        }

        .info-item {
            margin-bottom: 8px;
            color: #0056b3;
            font-size: 14px;
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }

        .download-section {
            text-align: center;
            margin-top: 30px;
        }

        .download-btn {
            display: inline-block;
            background: #007bff;
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-weight: 600;
            margin: 10px;
            transition: background 0.3s;
        }

        .download-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 한국투자증권 미국주식 자동매매</h1>
            <p>Korea Investment & Securities US Stock Auto Trading System</p>
        </div>

        <div class="main-content">
            <!-- 경고 패널 -->
            <div class="warning-panel">
                <div class="warning-title">⚠️ 중요한 안내사항</div>
                <div class="warning-item">• 현재 브라우저에서 직접 실행하는 독립 버전입니다.</div>
                <div class="warning-item">• CORS 정책으로 인해 실제 API 호출이 제한될 수 있습니다.</div>
                <div class="warning-item">• 완전한 기능을 위해서는 Node.js 또는 Python 서버가 필요합니다.</div>
                <div class="warning-item">• 실제 거래 전에 반드시 모의투자로 테스트하세요!</div>
            </div>

            <!-- 설치 안내 -->
            <div class="info-panel">
                <div class="info-title">🔧 완전한 기능을 위한 설치 안내</div>
                <div class="info-item">1. <strong>Node.js 설치</strong>: https://nodejs.org 에서 LTS 버전 다운로드</div>
                <div class="info-item">2. <strong>또는 Python 설치</strong>: https://python.org 에서 최신 버전 다운로드</div>
                <div class="info-item">3. 설치 후 start.bat 파일을 실행하면 완전한 서버 버전을 사용할 수 있습니다.</div>
            </div>

            <!-- 매매 전략 설명 -->
            <div class="info-panel">
                <div class="info-title">📈 구현된 매매 전략</div>
                <div class="info-item">• <strong>진입 조건</strong>: 개장 후 3분 → 1분봉 전고 돌파 + 거래액 $80+ + 상승률 20%+ + 1분봉 상승률 5%+</div>
                <div class="info-item">• <strong>매수</strong>: $1000 고정 금액</div>
                <div class="info-item">• <strong>손절</strong>: -5%</div>
                <div class="info-item">• <strong>익절</strong>: +10% (50% 매도), 최고가 대비 -18% (나머지 매도)</div>
            </div>

            <!-- API 설정 폼 (데모용) -->
            <h3 style="margin-bottom: 20px; color: #333;">API 설정 (데모 모드)</h3>
            
            <div class="form-group">
                <label for="environment">거래 환경</label>
                <select id="environment">
                    <option value="vps">모의투자</option>
                    <option value="prod">실전투자</option>
                </select>
            </div>

            <div class="form-group">
                <label for="appKey">App Key</label>
                <input type="text" id="appKey" placeholder="한국투자증권에서 발급받은 App Key를 입력하세요">
            </div>

            <div class="form-group">
                <label for="appSecret">App Secret</label>
                <input type="password" id="appSecret" placeholder="한국투자증권에서 발급받은 App Secret을 입력하세요">
            </div>

            <div class="form-group">
                <label for="accountNumber">계좌번호 (앞 8자리)</label>
                <input type="text" id="accountNumber" placeholder="계좌번호 앞 8자리를 입력하세요" maxlength="8">
            </div>

            <button class="btn" onclick="showDemo()">
                🔍 데모 모드 시작 (실제 거래 없음)
            </button>

            <div id="alertArea" style="margin-top: 20px;"></div>

            <!-- 다운로드 섹션 -->
            <div class="download-section">
                <h3 style="color: #333; margin-bottom: 20px;">필수 프로그램 다운로드</h3>
                <a href="https://nodejs.org" target="_blank" class="download-btn">
                    📦 Node.js 다운로드
                </a>
                <a href="https://python.org" target="_blank" class="download-btn">
                    🐍 Python 다운로드
                </a>
            </div>
        </div>
    </div>

    <script>
        function showAlert(message, type = 'info') {
            const alertArea = document.getElementById('alertArea');
            alertArea.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
            
            setTimeout(() => {
                alertArea.innerHTML = '';
            }, 5000);
        }

        function showDemo() {
            const appKey = document.getElementById('appKey').value.trim();
            const appSecret = document.getElementById('appSecret').value.trim();
            const accountNumber = document.getElementById('accountNumber').value.trim();
            const environment = document.getElementById('environment').value;

            if (!appKey || !appSecret || !accountNumber) {
                showAlert('모든 필드를 입력해주세요.', 'error');
                return;
            }

            if (accountNumber.length !== 8) {
                showAlert('계좌번호는 8자리여야 합니다.', 'error');
                return;
            }

            showAlert(`
                <strong>데모 모드 시작!</strong><br>
                • 거래 환경: ${environment === 'vps' ? '모의투자' : '실전투자'}<br>
                • 계좌: ${accountNumber}-01<br>
                • 상태: API 연결 시뮬레이션 중...<br>
                <br>
                <em>실제 거래를 위해서는 Node.js 또는 Python 서버가 필요합니다.</em>
            `, 'success');

            // 데모 시뮬레이션
            setTimeout(() => {
                showAlert(`
                    <strong>시뮬레이션 결과:</strong><br>
                    • AAPL 모니터링 중... ✓<br>
                    • TSLA 조건 확인 중... ✓<br>
                    • NVDA 가격 추적 중... ✓<br>
                    <br>
                    <em>실제 매매를 위해서는 완전한 서버 버전을 사용하세요!</em>
                `, 'warning');
            }, 3000);
        }

        // 페이지 로드 시 안내
        window.addEventListener('load', function() {
            showAlert(`
                <strong>독립 실행 버전입니다!</strong><br>
                완전한 기능을 위해 Node.js 또는 Python을 설치하고 start.bat을 실행하세요.
            `, 'warning');
        });
    </script>
</body>
</html>
