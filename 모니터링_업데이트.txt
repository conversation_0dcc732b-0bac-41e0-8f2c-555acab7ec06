========================================
🔍 동적 모니터링 시스템 업데이트! (v4.0)
========================================

✅ 새로 추가된 기능:
==================

1. 🎯 동적 종목 스캔 시스템
   - 실시간으로 조건 충족 종목 자동 선별
   - 상승률 + 거래금액 기준으로 필터링
   - 5분마다 모니터링 종목 자동 업데이트

2. ⚙️ 모니터링 조건 설정
   - 최소 상승률: 5% ~ 100% (기본값: 20%)
   - 최소 거래금액: 10만불 ~ 1000만불 (기본값: 80만불)
   - 최대 모니터링 종목 수: 5개 ~ 50개 (기본값: 20개)

3. 📊 모니터링 모드 선택
   - 동적 스캔: 조건 충족 종목 자동 선별
   - 고정 종목: 기본 7개 종목 (AAPL, TSLA, NVDA, MSFT, GOOGL, AMZN, META)

4. 🔍 즉시 스캔 기능
   - "즉시 스캔" 버튼으로 수동 스캔 실행
   - 실시간 스캔 결과 확인
   - 조건 충족 종목 즉시 반영

5. 📈 확장된 종목 풀
   - 50개 주요 미국 종목에서 스캔
   - 테크, 핀테크, 클라우드, AI 관련 종목 포함
   - 상승률 순으로 자동 정렬

🔧 사용 방법:
============

1. 모니터링 설정 조정
   - 최소 상승률 설정 (예: 20%)
   - 최소 거래금액 설정 (예: 80만불)
   - 최대 모니터링 종목 수 설정 (예: 20개)

2. 모니터링 모드 선택
   - 동적 스캔: 조건에 맞는 종목 자동 선별
   - 고정 종목: 기본 7개 종목 사용

3. 설정 적용 및 스캔
   - "설정 적용" 버튼 클릭
   - "즉시 스캔" 버튼으로 수동 스캔
   - 자동매매 시작 시 자동 스캔 실행

📊 스캔 로직:
===========

1. 종목 풀 순회 (50개 주요 종목)
2. 각 종목별 데이터 수집:
   - 현재가, 거래량, 상승률
   - 거래금액 계산 (현재가 × 거래량)

3. 조건 필터링:
   - 상승률 ≥ 설정값 (예: 20%)
   - 거래금액 ≥ 설정값 (예: 80만불)

4. 결과 정렬 및 선별:
   - 상승률 순으로 정렬
   - 상위 N개 종목 선별 (최대 설정값)

🎯 동적 스캔 예시:
================

설정: 상승률 20%+, 거래금액 80만불+, 최대 20개

스캔 결과:
✅ NVDA: +25.3%, 1,200만불
✅ TSLA: +22.1%, 950만불  
✅ AMD: +21.8%, 680만불
✅ AAPL: +20.5%, 2,100만불
...

→ 상위 20개 종목이 모니터링 대상으로 자동 설정

🔄 자동 업데이트:
===============

1. 초기 스캔: 자동매매 시작 시
2. 정기 스캔: 5분마다 자동 실행
3. 수동 스캔: "즉시 스캔" 버튼 클릭 시
4. 실시간 반영: 스캔 결과 즉시 적용

📈 장점:
=======

1. 기회 포착 극대화
   - 급등 종목 자동 발견
   - 놓치는 기회 최소화

2. 효율적 자원 활용
   - 활발한 종목에만 집중
   - API 호출 최적화

3. 유연한 설정
   - 시장 상황에 맞는 조건 설정
   - 개인 투자 성향 반영

4. 실시간 적응
   - 시장 변화에 자동 대응
   - 지속적인 모니터링 최적화

⚠️ 주의사항:
===========

1. API 호출 제한
   - 스캔 시 API 호출량 증가
   - 적절한 딜레이 설정됨

2. 네트워크 상태
   - 안정적인 인터넷 연결 필요
   - 스캔 실패 시 기본 종목 사용

3. 설정 최적화
   - 너무 낮은 조건 설정 시 과다 종목 선별
   - 너무 높은 조건 설정 시 종목 부족

🚀 업데이트 완료!
===============

이제 시장에서 가장 활발한 종목들을 자동으로 찾아서 
모니터링하고 거래할 수 있습니다!

브라우저를 새로고침하고 새로운 동적 모니터링 기능을 
확인해보세요!

========================================
📊 더 스마트한 자동매매가 시작됩니다!
========================================
