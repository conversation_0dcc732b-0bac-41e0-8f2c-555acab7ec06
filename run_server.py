import http.server
import socketserver
import webbrowser
import os
import threading
import time

PORT = 3000

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        super().end_headers()

    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

def start_server():
    try:
        os.chdir(os.path.dirname(os.path.abspath(__file__)))
        
        with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
            print("=" * 60)
            print("🚀 한국투자증권 미국주식 자동매매 시스템")
            print("   Korea Investment US Stock Auto Trading System")
            print("=" * 60)
            print(f"📡 서버가 포트 {PORT}에서 실행 중입니다.")
            print(f"🌐 브라우저에서 http://localhost:{PORT} 으로 접속하세요.")
            print("⏹️  종료하려면 Ctrl+C를 누르세요.")
            print("=" * 60)
            
            # 3초 후 브라우저 자동 열기
            def open_browser():
                time.sleep(3)
                try:
                    webbrowser.open(f'http://localhost:{PORT}')
                    print("🔗 브라우저가 자동으로 열렸습니다.")
                except:
                    print("🔗 브라우저를 수동으로 열어주세요.")
            
            threading.Thread(target=open_browser, daemon=True).start()
            
            httpd.serve_forever()
            
    except OSError as e:
        print(f"❌ 오류: 포트 {PORT}이 사용 중이거나 권한이 없습니다.")
        print(f"   오류 상세: {e}")
        print("   다른 프로그램을 종료하거나 관리자 권한으로 실행해보세요.")
        input("엔터를 눌러 종료...")
    except KeyboardInterrupt:
        print("\n👋 서버를 종료합니다.")
    except Exception as e:
        print(f"❌ 예상치 못한 오류: {e}")
        input("엔터를 눌러 종료...")

if __name__ == "__main__":
    start_server()
